# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Admin Bypass

For development work requiring admin access, a secure bypass system is available:

### Quick Start
```bash
# Enable development admin bypass
npm run dev:bypass:enable

# Start development server
npm run dev

# Check bypass status
npm run dev:bypass:status

# Disable bypass when done
npm run dev:bypass:disable
```

### Security Features
- **Multiple Safety Layers**: 5+ checks prevent production activation
- **Audit Logging**: All bypass usage is logged
- **Visual Indicators**: Clear warnings when bypass is active
- **Authentication Required**: Users must still be logged in

### Documentation
See `memory-docs/references/development-admin-bypass.md` for complete documentation.

## Tech Stack & Architecture

This is a full-stack business directory application built with a modern, decoupled architecture:

**Frontend (ui/):**
- React 19 + TypeScript + Vite
- Tailwind CSS 4.x with ShadCN/UI components
- React Router for navigation
- Firebase Authentication (with emulator support for local dev)
- Theme system with dark/light mode

**Backend (server/):**
- Hono API framework (Node.js runtime)
- Drizzle ORM with PostgreSQL
- Firebase Admin SDK for auth verification
- Cloudflare Workers deployment ready

**Database:**
- PostgreSQL with embedded database for local development
- Production-ready with Neon/Supabase/custom PostgreSQL

## Development Commands

**Start full development environment:**
```bash
pnpm run dev
```
This starts UI (port 5173), API (port 8787), embedded PostgreSQL, and Firebase auth emulator automatically.

**Individual services:**
```bash
# Frontend only
cd ui && pnpm dev

# Backend only
cd server && pnpm dev

# Build frontend
cd ui && pnpm build

# Database operations
cd server && pnpm db:push
```

**Testing:**
```bash
# Frontend
cd ui && pnpm lint

# Backend (when tests exist)
cd server && pnpm test
```

**Package management:**
- Use `pnpm add` for installing packages
- Use `npx shadcn add` for new ShadCN components

## Project Structure & Key Patterns

**Authentication Flow:**
- Local development uses Firebase emulator (any email/password works)
- Production uses Firebase Auth with Google Sign-In
- Backend middleware verifies Firebase tokens
- Admin routes protected by admin middleware

**Database Schema (server/src/schema/):**
- `users.ts` - User management
- `business.ts` - Business directory (businesses, categories, reviews, applications, hours)

**API Architecture:**
- Public routes: business listings, applications, reviews
- Protected routes (`/api/v1/protected`): user profile
- Admin routes (`/api/v1/admin`): business/category management

**Frontend Routing:**
- Public directory: `/`, `/search`, `/business/:slug`, `/categories`
- Admin panel: `/admin/*` (requires authentication)

**State Management:**
- Auth context: `ui/src/lib/auth-context.tsx`
- Theme provider: `ui/src/components/theme-provider.tsx`
- API communication: `ui/src/lib/serverComm.ts`

## Environment Configuration

**Local Development (default):**
- Embedded PostgreSQL database
- Firebase auth emulator
- No external services required

**Production Services:**
- Connect with `pnpm connect:auth` (Firebase)
- Connect with `pnpm connect:database` (PostgreSQL)
- Deploy with `pnpm connect:deploy` (Cloudflare)

## Agent System

This project includes a comprehensive agent system located in the `/agents` folder. These agents provide specialized assistance for development tasks:

### Core Quality Agents (`ClaudeCodeAgents-master/`)

**Jenny** - Implementation verification agent that verifies implemented code matches project specifications:
- Validates authentication systems against security requirements
- Ensures database schemas implement specifications correctly
- Provides independent assessment of feature completeness

**Claude MD Compliance Checker** - Ensures code changes adhere to CLAUDE.md guidelines:
- Verifies implementations follow project standards
- Checks modifications comply with coding guidelines
- Validates consistency with documented principles

**Code Quality Pragmatist** - Reviews code for over-engineering and unnecessary complexity:
- Identifies over-engineered solutions and unnecessary abstractions
- Spots premature optimizations
- Ensures code remains simple and maintainable

**Karen** - Reality check agent that provides realistic assessment of project completion:
- Assesses actual vs claimed project completion
- Creates actionable plans to finish work
- Validates functional completeness without over-engineering

**Task Completion Validator** - Verifies claimed task completions are actually functional:
- Validates authentication systems work end-to-end
- Ensures database integrations are functional
- Confirms implementations meet underlying goals

**UI Comprehensive Tester** - Performs thorough UI testing across platforms:
- Automatically selects testing tools (Puppeteer, Playwright)
- Tests user flows and edge cases
- Validates functionality across different platforms

### Development Agents (`core/`)

**code-reviewer** - Security-aware quality gate that MUST BE USED before merging:
- Delivers detailed review reports with severity tags
- Routes security/performance issues to specialist agents
- Ensures code is secure, maintainable, and performant

**code-archaeologist** - Legacy codebase analysis and documentation
**documentation-specialist** - Documentation maintenance and creation
**performance-optimizer** - Performance analysis and optimization
**prd-generator** - Product requirement document creation

### Framework Specialists (`specialized/`)

**react-component-architect** - Expert React architect for modern patterns:
- React 19 + Next.js 14+ with App Router and Server Components
- shadcn/ui and Tailwind CSS integration
- Accessible, tested, reusable components

**Django/Laravel/Rails experts** - Backend framework specialists
**Vue specialists** - Vue.js development experts

### Universal Agents (`universal/`)

**frontend-developer** - Universal UI builder for responsive, accessible UIs:
- Works with any frontend framework or vanilla JS/TS
- Mobile-first, progressive enhancement approach
- Performance budgets and accessibility compliance

**backend-developer** - Backend architecture and API development
**api-architect** - API design and architecture
**tailwind-css-expert** - Tailwind CSS styling expertise

### Usage Guidelines

- Use agents PROACTIVELY based on task type
- Reference agents by name when specialized expertise is needed
- Agents provide structured output formats for consistent results
- Each agent has specific tools and responsibilities defined in their markdown files

## Important Development Notes

**Database Operations:**
- Schema changes: Edit `server/src/schema/*` then run `pnpm db:push`
- Local data persists in `data/postgres/`
- Firebase emulator data persists in `data/firebase-emulator/`

**Component Development:**
- Use existing ShadCN components from `ui/src/components/ui/`
- Follow theme system patterns for consistent styling
- Maintain responsive design principles

**API Development:**
- Protected routes require `authMiddleware`
- Admin routes require `adminMiddleware`
- Use Drizzle ORM patterns for database operations
- Follow existing error handling patterns

**Code Style:**
- TypeScript throughout
- Tailwind CSS for styling
- React functional components with hooks
- Async/await for API calls

## Business Logic

This application implements a business directory platform with:
- Public business listings and search
- User review system (requires admin approval)
- Business application workflow
- Admin dashboard for content management
- Category-based organization
- Featured business promotion

The codebase uses modular routing patterns and clean separation between public and admin functionality.

## System Rules & Development Guidelines

### Environment & Editing Standards
- **Environment**: Use gitbash for command line operations
- **Code Verification**: Always check existing code before editing
- **Line Number Accuracy**: Double-check line numbers when making edits
- **Read Before Edit**: Always read lines before making modifications
- **Preserve Functionality**: Maintain all existing functionality during changes
- **Validation**: Double-check all edits after completion

### Code Structure Principles
**Goal**: Maintain a scalable, modular codebase by keeping files concise, separating concerns, and using consistent structure across components.

**File Structure Rules**:
- **One Responsibility Per File**: Each file should have a single, clear purpose
- **Maximum File Length**: 500 lines maximum per file
- **Modular Design**: Break complex functionality into smaller, focused modules
- **Relative Imports**: Use relative imports within the project structure
- **Example Structure**:
  - UI Component: `components/Button.tsx`
  - API Handler: `api/fetchUser.ts`
  - Utility: `utils/dateFormat.ts`

### Development Philosophy
- **KISS (Keep It Simple)**: Prioritize simplicity over clever or complex solutions
- **YAGNI (You Aren't Gonna Need It)**: Don't implement features unless needed now
- **Component First**: Components should be reusable, composable, and self-contained
- **Performance By Default**: Focus on clean, readable code; let React 19's compiler optimize performance

### Design Principles
- **Vertical Slice Architecture**: Organize by feature rather than technical layer
- **Composition Over Inheritance**: Prefer composition patterns
- **Fail Fast Validation**: Validate inputs early and provide clear error messages

### AI Behavior Guidelines
- **No Assumptions**: Never assume missing context; ask questions if uncertain
- **No Hallucinated Functions**: Only use known, verified libraries and functions
- **Confirm Paths**: Always verify file paths and module names exist before referencing
- **Never Delete Existing Code**: Don't remove code unless explicitly instructed
- **Follow Rules**: Strictly adhere to all established guidelines

### Implementation Rules
1. Always follow implementation plans generated by Context Agents
2. Do not make changes without valid context and plan
3. Maintain all existing functionality and UI
4. Ensure backwards compatibility
5. Complete all related updates; no partial implementations
6. Write modular, maintainable code; avoid large monolithic files

### Development Guidelines
- **Context Awareness**: Understand the full context before making changes
- **Reuse Utilities**: Use existing utility functions and components
- **Prefer Composition**: Build complex functionality through composition
- **Pattern Consistency**: Follow established patterns throughout the codebase
- **Check Other Domains**: Consider impact on other parts of the system

### Common Pitfalls to Avoid
- **Duplicate Functionality**: Check for existing implementations before creating new ones
- **Overwriting Tests**: Don't modify or delete existing tests without cause
- **Core Framework Modifications**: Don't modify core frameworks without permission
- **Missing Dependencies**: Always check for existing dependencies before adding new ones

### Integration Checklist
Before completing any task, verify:
- [ ] Full integration into the system structure
- [ ] All relevant imports, styles, and modules updated
- [ ] No production breaks due to isolation or misalignment
- [ ] All connected systems updated (routing, state, dependencies, styles)

### Agent System
When an agent is referenced or called, assume the role and capabilities of that specific agent.

### Development Workflow
- **Test-First Development**: Preferred approach when applicable
- **Think Hard Before Architecting**: Plan thoroughly before implementation
- **Break Tasks Into Units**: Divide complex tasks into manageable pieces
- **Validate Before Building**: Ensure requirements are clear before starting