import { useAuth } from '@/lib/auth-context'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle } from 'lucide-react'

/**
 * Development Admin Bypass Indicator
 * Shows a warning banner when development admin bypass is active
 * This component should be placed in the admin layout or dashboard
 */
export function DevBypassIndicator() {
  const { isDevBypassActive } = useAuth()

  if (!isDevBypassActive) {
    return null
  }

  return (
    <Alert variant="destructive" className="mb-4 border-orange-500 bg-orange-50 text-orange-900">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="font-medium">
        🔓 <strong>DEVELOPMENT MODE:</strong> Admin bypass is active. This grants admin access to any authenticated user. 
        This should <strong>NEVER</strong> be enabled in production!
      </AlertDescription>
    </Alert>
  )
}

/**
 * Compact version for header/navbar use
 */
export function DevBypassBadge() {
  const { isDevBypassActive } = useAuth()

  if (!isDevBypassActive) {
    return null
  }

  return (
    <div className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-orange-900 bg-orange-100 border border-orange-300 rounded-md">
      <AlertTriangle className="h-3 w-3" />
      DEV BYPASS
    </div>
  )
}
