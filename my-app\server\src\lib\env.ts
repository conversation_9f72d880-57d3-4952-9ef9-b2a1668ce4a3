/**
 * Cross-platform environment variable utilities
 * Works with both Node.js (process.env) and Cloudflare Workers (c.env)
 */

type EnvLike = Record<string, string | undefined>;

let contextEnv: EnvLike | null = null;

export function setEnvContext(env: any) {
  contextEnv = env;
}

export function clearEnvContext() {
  contextEnv = null;
}

function getEnvSource(): EnvLike {
  return contextEnv || process.env;
}

/**
 * Get environment variable with fallback support
 * Works in both Node.js and Cloudflare Workers environments
 */
export function getEnv(key: string, defaultValue?: string): string | undefined {
  const value = getEnvSource()[key];
  return value !== undefined ? value : defaultValue;
}

/**
 * Get required environment variable, throws if missing
 */
export function getRequiredEnv(key: string): string {
  const value = getEnv(key);
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
}

/**
 * Check if we're in development mode
 * Works across Node.js and Cloudflare Workers
 */
export function isDevelopment(): boolean {
  return getEnv('NODE_ENV') === 'development' ||
         getEnv('FIREBASE_AUTH_EMULATOR_HOST') !== undefined;
}

/**
 * Check if development admin bypass is enabled
 * SECURITY: Multiple safety checks to prevent production activation
 */
export function isDevAdminBypassEnabled(): boolean {
  // First check: Must be in development mode
  if (!isDevelopment()) {
    return false;
  }

  // Second check: Must have explicit bypass flag
  const bypassFlag = getEnv('DEV_ADMIN_BYPASS');
  if (bypassFlag !== 'true') {
    return false;
  }

  // Third check: Must have Firebase emulator running (additional safety)
  const firebaseEmulatorHost = getEnv('FIREBASE_AUTH_EMULATOR_HOST');
  if (!firebaseEmulatorHost) {
    return false;
  }

  // Fourth check: Must NOT be in Cloudflare Workers environment
  if (isCloudflareEnv(getEnvSource())) {
    console.warn('🚨 DEV_ADMIN_BYPASS attempted in Cloudflare Workers - BLOCKED');
    return false;
  }

  // Fifth check: Must NOT have production indicators
  if (isProductionEnvironment()) {
    console.warn('🚨 DEV_ADMIN_BYPASS attempted in production environment - BLOCKED');
    return false;
  }

  return true;
}

/**
 * Detect if we're in a production environment
 * Multiple indicators to catch various production deployments
 */
export function isProductionEnvironment(): boolean {
  const envSource = getEnvSource();

  // Check for common production environment variables
  const productionIndicators = [
    envSource.VERCEL === '1',
    envSource.NETLIFY === 'true',
    envSource.RAILWAY_ENVIRONMENT === 'production',
    envSource.RENDER === 'true',
    envSource.NODE_ENV === 'production',
    envSource.RUNTIME === 'cloudflare',
    // Check for production database URLs
    envSource.DATABASE_URL?.includes('neon.tech'),
    envSource.DATABASE_URL?.includes('supabase.co'),
    envSource.DATABASE_URL?.includes('planetscale.com'),
    envSource.DATABASE_URL?.includes('railway.app'),
    // Check for production Firebase project
    !envSource.FIREBASE_AUTH_EMULATOR_HOST && envSource.FIREBASE_PROJECT_ID !== 'demo-project'
  ];

  return productionIndicators.some(indicator => indicator === true);
}

/**
 * Log development admin bypass usage for audit trail
 */
export function logDevAdminBypass(action: string, userId?: string, details?: any): void {
  if (!isDevAdminBypassEnabled()) {
    return;
  }

  const logEntry = {
    timestamp: new Date().toISOString(),
    action,
    userId: userId || 'unknown',
    environment: {
      NODE_ENV: getEnv('NODE_ENV'),
      FIREBASE_AUTH_EMULATOR_HOST: getEnv('FIREBASE_AUTH_EMULATOR_HOST'),
      DEV_ADMIN_BYPASS: getEnv('DEV_ADMIN_BYPASS')
    },
    details
  };

  console.warn('🔓 DEV_ADMIN_BYPASS:', JSON.stringify(logEntry, null, 2));
}

/**
 * Get database URL from environment
 */
export function getDatabaseUrl(): string | undefined {
  return getEnv('DATABASE_URL');
}

/**
 * Check if DATABASE_URL points to local PostgreSQL database server
 */
export function isLocalEmbeddedPostgres(): boolean {
  const dbUrl = getDatabaseUrl();
  // Check if it's a localhost PostgreSQL connection (local database server)
  return dbUrl ? (dbUrl.includes('localhost:') && dbUrl.includes('postgres:password')) : false;
}

/**
 * Get Firebase project ID from environment
 */
export function getFirebaseProjectId(): string {
  return getRequiredEnv('FIREBASE_PROJECT_ID');
}

/**
 * For Node.js environments - get process.env
 */
export function getNodeEnv() {
  return process.env;
}

/**
 * Type guard to check if we're in a Cloudflare Workers environment
 */
export function isCloudflareEnv(source: EnvLike): boolean {
  // In Cloudflare Workers, process.env is not available or is empty
  return typeof process === 'undefined' || Object.keys(process.env).length === 0;
} 