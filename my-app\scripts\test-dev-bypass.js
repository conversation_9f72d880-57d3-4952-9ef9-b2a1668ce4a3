#!/usr/bin/env node

/**
 * Test script for Development Admin Bypass
 * 
 * This script tests the bypass functionality and safety mechanisms
 */

const { isDevAdminBypassEnabled, isProductionEnvironment, logDevAdminBypass } = require('../server/src/lib/env.ts');

async function testBypassDetection() {
  console.log('🧪 Testing Development Admin Bypass Detection');
  console.log('==============================================');
  console.log('');
  
  // Test environment detection
  console.log('Environment Variables:');
  console.log(`  NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`  DEV_ADMIN_BYPASS: ${process.env.DEV_ADMIN_BYPASS || 'not set'}`);
  console.log(`  FIREBASE_AUTH_EMULATOR_HOST: ${process.env.FIREBASE_AUTH_EMULATOR_HOST || 'not set'}`);
  console.log('');
  
  // Test production detection
  const isProduction = isProductionEnvironment();
  console.log(`Production Environment: ${isProduction ? '🚨 YES' : '✅ NO'}`);
  
  // Test bypass detection
  const bypassEnabled = isDevAdminBypassEnabled();
  console.log(`Bypass Enabled: ${bypassEnabled ? '🔓 YES' : '🔒 NO'}`);
  console.log('');
  
  if (bypassEnabled) {
    console.log('✅ BYPASS ACTIVE - Testing audit logging...');
    logDevAdminBypass('test_action', 'test_user', { test: true });
  } else {
    console.log('❌ BYPASS INACTIVE - This is expected if not properly configured');
  }
  
  console.log('');
  console.log('Safety Check Results:');
  console.log('====================');
  
  // Test individual safety checks
  const isDev = process.env.NODE_ENV === 'development' || process.env.FIREBASE_AUTH_EMULATOR_HOST;
  const hasFlag = process.env.DEV_ADMIN_BYPASS === 'true';
  const hasEmulator = process.env.FIREBASE_AUTH_EMULATOR_HOST;
  
  console.log(`✓ Development Mode: ${isDev ? 'PASS' : 'FAIL'}`);
  console.log(`✓ Bypass Flag Set: ${hasFlag ? 'PASS' : 'FAIL'}`);
  console.log(`✓ Firebase Emulator: ${hasEmulator ? 'PASS' : 'FAIL'}`);
  console.log(`✓ Not Production: ${!isProduction ? 'PASS' : 'FAIL'}`);
  
  const allChecksPass = isDev && hasFlag && hasEmulator && !isProduction;
  console.log('');
  console.log(`Overall Status: ${allChecksPass ? '🟢 ALL CHECKS PASS' : '🔴 SOME CHECKS FAIL'}`);
  
  if (!allChecksPass) {
    console.log('');
    console.log('💡 To enable bypass:');
    console.log('   npm run dev:bypass:enable');
  }
}

// Test production safety
function testProductionSafety() {
  console.log('');
  console.log('🛡️  Testing Production Safety Mechanisms');
  console.log('=========================================');
  console.log('');
  
  // Simulate production environment variables
  const productionTests = [
    { name: 'Vercel', env: { VERCEL: '1' } },
    { name: 'Netlify', env: { NETLIFY: 'true' } },
    { name: 'Railway', env: { RAILWAY_ENVIRONMENT: 'production' } },
    { name: 'Render', env: { RENDER: 'true' } },
    { name: 'Production NODE_ENV', env: { NODE_ENV: 'production' } },
    { name: 'Cloudflare Workers', env: { RUNTIME: 'cloudflare' } }
  ];
  
  productionTests.forEach(test => {
    // Temporarily set environment variable
    const originalEnv = {};
    Object.keys(test.env).forEach(key => {
      originalEnv[key] = process.env[key];
      process.env[key] = test.env[key];
    });
    
    const isProduction = isProductionEnvironment();
    console.log(`${test.name}: ${isProduction ? '🚨 BLOCKED' : '❌ NOT DETECTED'}`);
    
    // Restore original environment
    Object.keys(test.env).forEach(key => {
      if (originalEnv[key] !== undefined) {
        process.env[key] = originalEnv[key];
      } else {
        delete process.env[key];
      }
    });
  });
}

// Main execution
async function main() {
  try {
    await testBypassDetection();
    testProductionSafety();
    
    console.log('');
    console.log('🎯 Test Complete');
    console.log('================');
    console.log('');
    console.log('Next Steps:');
    console.log('1. Enable bypass: npm run dev:bypass:enable');
    console.log('2. Start development server: npm run dev');
    console.log('3. Check bypass status: npm run dev:bypass:status');
    console.log('4. Test admin access in browser');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

main();
