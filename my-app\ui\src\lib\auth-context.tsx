import { createContext, useContext, useEffect, useState } from 'react'
import type { User } from 'firebase/auth'
import { onAuthStateChanged } from 'firebase/auth'
import { auth } from './firebase'

// Check if development admin bypass is enabled
function isDevAdminBypassEnabled(): boolean {
  return import.meta.env.DEV &&
         import.meta.env.VITE_DEV_ADMIN_BYPASS === 'true' &&
         import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true';
}

// Backend admin verification function
async function verifyAdminWithBackend(user: User): Promise<boolean> {
  try {
    const token = await user.getIdToken()
    const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5500'}/api/v1/protected/admin-status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (!response.ok) {
      return false
    }

    const adminData = await response.json()
    return adminData.isAdmin === true
  } catch (error) {
    console.error('Backend admin verification failed:', error)
    return false
  }
}

type AuthContextType = {
  user: User | null
  loading: boolean
  isAdmin: boolean
  checkingAdmin: boolean
  isDevBypassActive: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  isAdmin: false,
  checkingAdmin: false,
  isDevBypassActive: false
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)
  const [checkingAdmin, setCheckingAdmin] = useState(false)
  const [isDevBypassActive] = useState(isDevAdminBypassEnabled())

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user)
      setLoading(false)

      if (user) {
        setCheckingAdmin(true)
        try {
          // Get the user's ID token to check custom claims
          const idTokenResult = await user.getIdTokenResult()

          // First try to get admin status from custom claims
          let isAdmin = idTokenResult.claims.admin === true

          // If custom claims don't work, verify with backend
          if (!isAdmin) {
            isAdmin = await verifyAdminWithBackend(user)
          }

          // If development bypass is active, grant admin access to any authenticated user
          if (isDevBypassActive && user) {
            isAdmin = true
            console.warn('🔓 DEV_ADMIN_BYPASS: Frontend granting admin access to authenticated user')
          }

          setIsAdmin(isAdmin)
        } catch (error) {
          console.error('Error checking admin status:', error)
          setIsAdmin(false)
        } finally {
          setCheckingAdmin(false)
        }
      } else {
        setIsAdmin(false)
        setCheckingAdmin(false)
      }
    })

    return () => unsubscribe()
  }, [])

  // Show development bypass warning in console
  useEffect(() => {
    if (isDevBypassActive) {
      console.warn('🔓 DEV_ADMIN_BYPASS: Development admin bypass is ACTIVE. This should NEVER be enabled in production!');
    }
  }, [isDevBypassActive]);

  return (
    <AuthContext.Provider value={{ user, loading, isAdmin, checkingAdmin, isDevBypassActive }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext) 