#!/usr/bin/env node

/**
 * Development Admin Bypass Management Script
 * 
 * This script helps developers enable/disable the development admin bypass
 * with proper safety checks and warnings.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const ENV_FILE = path.join(__dirname, '..', '.env.local');

function readEnvFile() {
  if (!fs.existsSync(ENV_FILE)) {
    return {};
  }
  
  const content = fs.readFileSync(ENV_FILE, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      env[key.trim()] = valueParts.join('=').trim();
    }
  });
  
  return env;
}

function writeEnvFile(env) {
  const content = Object.entries(env)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n') + '\n';
  
  fs.writeFileSync(ENV_FILE, content);
}

function isProductionEnvironment() {
  const env = process.env;
  return env.NODE_ENV === 'production' ||
         env.VERCEL === '1' ||
         env.NETLIFY === 'true' ||
         env.RAILWAY_ENVIRONMENT === 'production' ||
         env.RENDER === 'true' ||
         env.RUNTIME === 'cloudflare';
}

function enableBypass() {
  if (isProductionEnvironment()) {
    console.error('🚨 ERROR: Cannot enable admin bypass in production environment!');
    process.exit(1);
  }
  
  console.log('🔓 Enabling development admin bypass...');
  
  const env = readEnvFile();
  
  // Set required environment variables
  env.DEV_ADMIN_BYPASS = 'true';
  env.VITE_DEV_ADMIN_BYPASS = 'true';
  env.NODE_ENV = 'development';
  env.VITE_USE_FIREBASE_EMULATOR = 'true';
  
  // Ensure Firebase emulator is configured
  if (!env.FIREBASE_AUTH_EMULATOR_HOST) {
    env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:5503';
  }
  if (!env.VITE_FIREBASE_AUTH_EMULATOR_PORT) {
    env.VITE_FIREBASE_AUTH_EMULATOR_PORT = '5503';
  }
  
  writeEnvFile(env);
  
  console.log('✅ Development admin bypass enabled!');
  console.log('');
  console.log('⚠️  SECURITY WARNING:');
  console.log('   - This grants admin access to ANY authenticated user');
  console.log('   - This should NEVER be enabled in production');
  console.log('   - All bypass usage is logged for audit purposes');
  console.log('');
  console.log('📋 Next steps:');
  console.log('   1. Restart your development server');
  console.log('   2. Start Firebase emulators if not running');
  console.log('   3. Login with any user account');
  console.log('   4. Access admin functionality');
  console.log('');
  console.log('🔧 To disable: npm run dev:bypass:disable');
}

function disableBypass() {
  console.log('🔒 Disabling development admin bypass...');
  
  const env = readEnvFile();
  
  // Remove or set bypass flags to false
  env.DEV_ADMIN_BYPASS = 'false';
  env.VITE_DEV_ADMIN_BYPASS = 'false';
  
  writeEnvFile(env);
  
  console.log('✅ Development admin bypass disabled!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('   1. Restart your development server');
  console.log('   2. Admin access now requires proper Firebase admin privileges');
  console.log('');
  console.log('🔧 To enable: npm run dev:bypass:enable');
}

function showStatus() {
  const env = readEnvFile();
  const processEnv = process.env;
  
  console.log('🔍 Development Admin Bypass Status');
  console.log('=====================================');
  console.log('');
  
  const bypassEnabled = env.DEV_ADMIN_BYPASS === 'true';
  const frontendBypassEnabled = env.VITE_DEV_ADMIN_BYPASS === 'true';
  const isDev = env.NODE_ENV === 'development' || processEnv.NODE_ENV === 'development';
  const hasEmulator = env.FIREBASE_AUTH_EMULATOR_HOST || processEnv.FIREBASE_AUTH_EMULATOR_HOST;
  const isProduction = isProductionEnvironment();
  
  console.log(`Backend Bypass:     ${bypassEnabled ? '🔓 ENABLED' : '🔒 DISABLED'}`);
  console.log(`Frontend Bypass:    ${frontendBypassEnabled ? '🔓 ENABLED' : '🔒 DISABLED'}`);
  console.log(`Development Mode:   ${isDev ? '✅ YES' : '❌ NO'}`);
  console.log(`Firebase Emulator:  ${hasEmulator ? '✅ CONFIGURED' : '❌ NOT CONFIGURED'}`);
  console.log(`Production Env:     ${isProduction ? '🚨 YES (BYPASS BLOCKED)' : '✅ NO'}`);
  console.log('');
  
  if (bypassEnabled && frontendBypassEnabled && isDev && hasEmulator && !isProduction) {
    console.log('🟢 Status: BYPASS ACTIVE - Admin access granted to authenticated users');
  } else if (bypassEnabled || frontendBypassEnabled) {
    console.log('🟡 Status: BYPASS PARTIALLY CONFIGURED - May not work properly');
  } else {
    console.log('🔴 Status: BYPASS DISABLED - Normal admin privileges required');
  }
  
  console.log('');
  console.log('Environment Variables:');
  console.log(`  DEV_ADMIN_BYPASS=${env.DEV_ADMIN_BYPASS || 'not set'}`);
  console.log(`  VITE_DEV_ADMIN_BYPASS=${env.VITE_DEV_ADMIN_BYPASS || 'not set'}`);
  console.log(`  NODE_ENV=${env.NODE_ENV || processEnv.NODE_ENV || 'not set'}`);
  console.log(`  FIREBASE_AUTH_EMULATOR_HOST=${env.FIREBASE_AUTH_EMULATOR_HOST || processEnv.FIREBASE_AUTH_EMULATOR_HOST || 'not set'}`);
}

function showHelp() {
  console.log('Development Admin Bypass Management');
  console.log('===================================');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/dev-admin-bypass.js <command>');
  console.log('');
  console.log('Commands:');
  console.log('  enable    Enable development admin bypass');
  console.log('  disable   Disable development admin bypass');
  console.log('  status    Show current bypass status');
  console.log('  help      Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/dev-admin-bypass.js enable');
  console.log('  node scripts/dev-admin-bypass.js status');
  console.log('  node scripts/dev-admin-bypass.js disable');
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'enable':
    enableBypass();
    break;
  case 'disable':
    disableBypass();
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    console.error('Unknown command:', command);
    console.log('');
    showHelp();
    process.exit(1);
}
